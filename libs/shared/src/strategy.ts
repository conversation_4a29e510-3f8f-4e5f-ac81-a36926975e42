import fetch from 'node-fetch';
import process from 'node:process';
import { logging } from './logging';
import {
    StrategyProfile,
    GameAction,
    StrategyAction,
    StrategyResponse,
    BetProfile,
    FetchStrategyArgs,
} from './types';
import { featureFlagValue } from './featureFlags';
import { hashValue } from './hashing';

const strategyServiceUrl = process.env.STRATEGY_SERVICE_URL;
const strategyServiceToken = process.env.STRATEGY_SERVICE_TOKEN;

const strategyProfileData = (profileName?: string): StrategyProfile | null => {
    return { target_profile_name: profileName };
};

const betProfileData = (current_user_id: string): BetProfile => {
    return { current_user_id };
};

export const fetchStrategy = async ({
    state,
    mtt,
    service = 'RL',
    betProfileUserId,
    profileName,
    hints,
    exploiting_info,
}: FetchStrategyArgs): Promise<GameAction> => {
    const serviceUrl = await featureFlagValue('STRATEGY_SERVICE_URL_OVERRIDE', {}, strategyServiceUrl);
    const url = `${serviceUrl}/lookup?services=${service}`;
    const payload: any = {
        request_id: Date.now().toString(),
        hand: state,
        mtt,
        mode: 'bot',
    };

    if (betProfileUserId) {
        payload.bet_profile = betProfileData(betProfileUserId);
    }

    if (profileName) {
        payload.strategy_profile = strategyProfileData(profileName);
    }

    if (hints) {
        payload.hints = hints;
    }

    if (exploiting_info) {
        payload.exploiting_info = exploiting_info;
    }

    const startTime = Date.now();
    const response = await fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + strategyServiceToken,
        },
        body: JSON.stringify(obfuscate(payload)),
        signal: AbortSignal.timeout(8000),
    });

    if (!response.ok) {
        const errorText = await response.text();
        logging
            .withTag('STRATEGY_SERVICE_CALL')
            .error('error calling strategy service', response.statusText, {
                url,
                payload,
                status: response.status,
                errorText,
            });
        throw new Error(`error calling strategy service (${response.status}): ${errorText}`);
    }
    const duration = Date.now() - startTime;

    const responseData = (await response.json()) as StrategyResponse;

    if (!responseData || !responseData.strategy || !responseData.strategy.actions) {
        logging
            .withTag('STRATEGY_SERVICE_CALL')
            .warn('error calling strategy service', { url, payload, duration, response: responseData });
        throw new Error(
            `error calling strategy service (${response.status}): ${JSON.stringify(responseData)}`,
        );
    }

    let selectedAction: StrategyAction;
    let filteredActions = responseData.strategy.actions.filter((a) => a.hand_strategy != null);
    if (filteredActions.length > 0) {
        selectedAction = weightedRandomSelection(filteredActions, (action) => action.hand_strategy);
    } else {
        filteredActions = responseData.strategy.actions.filter((a) => a.action.action != 'allin');
        selectedAction = filteredActions[Math.floor(Math.random() * filteredActions.length)];
    }

    logServiceCall(url, payload, duration, responseData, selectedAction);

    return { ...selectedAction.action, probability: selectedAction.hand_strategy } as GameAction;
};

const obfuscate = (payload: any) => ({
    ...payload,
    hand: {
        ...payload.hand,
        gameuuid: hashValue(payload.hand.gameuuid),
        roomid: hashValue(payload.hand.roomid),
        players: payload.hand.players?.map(({ uid, ...rest }) => rest),
    },
});

export const weightedRandomSelection = <T>(items: T[], getWeight: (item: T) => number): T => {
    const totalWeight = items.reduce((sum, item) => sum + getWeight(item), 0);
    if (totalWeight === 0) return items[0];

    const randomNum = Math.random() * totalWeight;
    let runningWeightSum = 0;

    for (const item of items) {
        runningWeightSum += getWeight(item);
        if (randomNum <= runningWeightSum) {
            return item;
        }
    }

    return items.at(-1);
};

const logServiceCall = (
    url: string,
    payload: any,
    duration: number,
    response: StrategyResponse,
    action: StrategyAction,
) => {
    const serviceCallLog = {
        url,
        payload,
        duration,
        response: response.strategy.actions.reduce((acc: { [key: string]: any }, a: StrategyAction) => {
            acc[a.action_name] = {
                hand_strategy: a.hand_strategy,
                amount: a.action.amount,
                pot_size: a.action.pot_size,
            };
            return acc;
        }, {}),
        solution_information_source: response.solution_information?.source,
        bet_profile_config: response.solution_information?.bet_profile_config,
        strategy_profile_config: response.solution_information?.strategy_profile_config,
        action: action.action_name,
    };
    logging.withTag('STRATEGY_SERVICE_CALL').info('strategy service call', serviceCallLog);
};
