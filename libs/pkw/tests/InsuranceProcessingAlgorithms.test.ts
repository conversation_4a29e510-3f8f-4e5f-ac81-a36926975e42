import { mock, test } from 'node:test';
import assert from 'node:assert';
import { buildBuyInsuranceMsg } from '../src/pkw_ts/tools/InsuranceProcessingAlgorithms';
import { OutsCount, CoverageAmount } from '../src/pkw_ts/tools/Enum';

// Mock the logging module to avoid dependencies
mock.method(console, 'log', mock.fn());

test('buildBuyInsuranceMsg - should return is_buy: false when shouldBuyInsurance returns false', async () => {
    // Mock Math.random to always return 1 (higher than any probability)
    const originalRandom = Math.random;
    Math.random = () => 1;

    const result = buildBuyInsuranceMsg(1, 123, 456);

    assert.strictEqual(result.roomid, 123);
    assert.strictEqual(result.action_seq, 456);
    assert.strictEqual(result.is_buy, false);
    assert.strictEqual(result.amount, undefined);

    // Restore Math.random
    Math.random = originalRandom;
});

test('buildBuyInsuranceMsg - should return is_buy: true with coverage amount when shouldBuyInsurance returns true', async () => {
    // Mock Math.random to always return 0 (lower than any probability)
    const originalRandom = Math.random;
    Math.random = () => 0;

    const result = buildBuyInsuranceMsg(15, 123, 456);

    assert.strictEqual(result.roomid, 123);
    assert.strictEqual(result.action_seq, 456);
    assert.strictEqual(result.is_buy, true);
    assert.ok(result.amount !== undefined);
    assert.ok(Object.values(CoverageAmount).includes(result.amount!));

    // Restore Math.random
    Math.random = originalRandom;
});

test('shouldBuyInsurance probability increases with higher outsCount', async () => {
    const testCases = [
        { outsCount: 1, expectedProbability: 0.1 },
        { outsCount: OutsCount.LOW, expectedProbability: 0.1 },
        { outsCount: 5, expectedProbability: 0.25 },
        { outsCount: OutsCount.MEDIUM, expectedProbability: 0.25 },
        { outsCount: 10, expectedProbability: 0.35 },
        { outsCount: OutsCount.HIGH, expectedProbability: 0.35 },
        { outsCount: 15, expectedProbability: 0.55 },
        { outsCount: 20, expectedProbability: 0.55 },
    ];

    const originalRandom = Math.random;

    for (const testCase of testCases) {
        // Test with random value just below probability (should buy)
        Math.random = () => testCase.expectedProbability - 0.01;
        let result = buildBuyInsuranceMsg(testCase.outsCount, 123, 456);
        assert.strictEqual(
            result.is_buy,
            true,
            `Expected to buy insurance for outsCount ${testCase.outsCount} with probability ${testCase.expectedProbability}`,
        );

        // Test with random value just above probability (should not buy)
        Math.random = () => testCase.expectedProbability + 0.01;
        result = buildBuyInsuranceMsg(testCase.outsCount, 123, 456);
        assert.strictEqual(
            result.is_buy,
            false,
            `Expected NOT to buy insurance for outsCount ${testCase.outsCount} with probability ${testCase.expectedProbability}`,
        );
    }

    Math.random = originalRandom;
});

test('selectCoverageAmount - LOW outsCount should prefer smaller coverage amounts', async () => {
    const originalRandom = Math.random;
    const results: number[] = [];

    // Force buying insurance and collect coverage amounts
    Math.random = () => 0; // Always buy insurance

    // Run multiple times to test distribution
    for (let i = 0; i < 100; i++) {
        const result = buildBuyInsuranceMsg(OutsCount.LOW, 123, 456);
        if (result.is_buy && result.amount) {
            results.push(result.amount);
        }
    }

    // Verify that only expected coverage amounts are returned for LOW outsCount
    const expectedAmounts = [CoverageAmount.LOW, CoverageAmount.MEDIUM_LOW, CoverageAmount.MEDIUM_HIGH];
    for (const amount of results) {
        assert.ok(expectedAmounts.includes(amount), `Unexpected coverage amount ${amount} for LOW outsCount`);
    }

    Math.random = originalRandom;
});

test('selectCoverageAmount - MEDIUM outsCount should include balanced coverage', async () => {
    const originalRandom = Math.random;
    const results: number[] = [];

    Math.random = () => 0; // Always buy insurance

    for (let i = 0; i < 100; i++) {
        const result = buildBuyInsuranceMsg(OutsCount.MEDIUM, 123, 456);
        if (result.is_buy && result.amount) {
            results.push(result.amount);
        }
    }

    const expectedAmounts = [
        CoverageAmount.LOW,
        CoverageAmount.MEDIUM_LOW,
        CoverageAmount.MEDIUM_HIGH,
        CoverageAmount.BREAK_EAVEN,
    ];
    for (const amount of results) {
        assert.ok(
            expectedAmounts.includes(amount),
            `Unexpected coverage amount ${amount} for MEDIUM outsCount`,
        );
    }

    Math.random = originalRandom;
});

test('selectCoverageAmount - HIGH outsCount should prefer medium to high coverage', async () => {
    const originalRandom = Math.random;
    const results: number[] = [];

    Math.random = () => 0; // Always buy insurance

    for (let i = 0; i < 100; i++) {
        const result = buildBuyInsuranceMsg(OutsCount.HIGH, 123, 456);
        if (result.is_buy && result.amount) {
            results.push(result.amount);
        }
    }

    const expectedAmounts = [
        CoverageAmount.MEDIUM_LOW,
        CoverageAmount.MEDIUM_HIGH,
        CoverageAmount.BREAK_EAVEN,
        CoverageAmount.FULL_POT,
    ];
    for (const amount of results) {
        assert.ok(
            expectedAmounts.includes(amount),
            `Unexpected coverage amount ${amount} for HIGH outsCount`,
        );
    }

    Math.random = originalRandom;
});

test('Edge cases - boundary values for outsCount', async () => {
    const originalRandom = Math.random;

    // Test exact boundary values
    const boundaryTests = [
        { outsCount: 0, description: 'zero outsCount' },
        { outsCount: OutsCount.LOW, description: 'exactly LOW threshold' },
        { outsCount: OutsCount.LOW + 1, description: 'just above LOW threshold' },
        { outsCount: OutsCount.MEDIUM, description: 'exactly MEDIUM threshold' },
        { outsCount: OutsCount.MEDIUM + 1, description: 'just above MEDIUM threshold' },
        { outsCount: OutsCount.HIGH, description: 'exactly HIGH threshold' },
        { outsCount: OutsCount.HIGH + 1, description: 'just above HIGH threshold' },
        { outsCount: 100, description: 'very large outsCount' },
    ];

    for (const test of boundaryTests) {
        // Test that function doesn't throw errors
        Math.random = () => 0; // Always buy
        const result = buildBuyInsuranceMsg(test.outsCount, 123, 456);
        assert.ok(typeof result === 'object', `Should return object for ${test.description}`);
        assert.ok(typeof result.is_buy === 'boolean', `Should have boolean is_buy for ${test.description}`);

        if (result.is_buy) {
            assert.ok(
                typeof result.amount === 'number',
                `Should have numeric amount when buying for ${test.description}`,
            );
            assert.ok(result.amount > 0, `Amount should be positive for ${test.description}`);
        }
    }

    Math.random = originalRandom;
});

test('Edge cases - negative outsCount should be handled gracefully', async () => {
    const originalRandom = Math.random;
    Math.random = () => 0; // Always buy

    const result = buildBuyInsuranceMsg(-5, 123, 456);

    // Should not throw error and should return valid result
    assert.ok(typeof result === 'object');
    assert.ok(typeof result.is_buy === 'boolean');

    Math.random = originalRandom;
});

test('Weighted random selection - should respect weight distribution', async () => {
    const originalRandom = Math.random;

    // Test that weighted selection works correctly by controlling random values
    const testCases = [
        { randomValue: 0.1, outsCount: OutsCount.LOW, expectedInRange: [CoverageAmount.LOW] },
        { randomValue: 0.6, outsCount: OutsCount.LOW, expectedInRange: [CoverageAmount.MEDIUM_LOW] },
        { randomValue: 0.9, outsCount: OutsCount.LOW, expectedInRange: [CoverageAmount.MEDIUM_HIGH] },
    ];

    for (const testCase of testCases) {
        Math.random = () => testCase.randomValue;
        const result = buildBuyInsuranceMsg(testCase.outsCount, 123, 456);

        if (result.is_buy && result.amount) {
            // Note: Due to the weighted random algorithm, we can't predict exact values
            // but we can verify the result is within expected coverage amounts
            assert.ok(
                Object.values(CoverageAmount).includes(result.amount),
                `Coverage amount ${result.amount} should be a valid CoverageAmount`,
            );
        }
    }

    Math.random = originalRandom;
});

test('Weighted random selection - fallback mechanism', async () => {
    const originalRandom = Math.random;

    // Test edge case where random threshold might not match any weight
    Math.random = () => 0.999999; // Very high random value

    const result = buildBuyInsuranceMsg(OutsCount.LOW, 123, 456);

    if (result.is_buy && result.amount) {
        assert.ok(
            Object.values(CoverageAmount).includes(result.amount),
            'Should return valid coverage amount even with edge case random value',
        );
    }

    Math.random = originalRandom;
});

test('Insurance probability validation - correct probabilities for each range', async () => {
    // This test validates that the probability logic matches the memory about opponent outs
    // Higher opponent outs should increase insurance buy probability

    const originalRandom = Math.random;
    const testResults: { outsCount: number; shouldBuy: boolean }[] = [];

    // Test with a fixed random value that's between probabilities
    const testRandomValue = 0.3; // Between 0.25 and 0.35
    Math.random = () => testRandomValue;

    const testCases = [
        { outsCount: 1, expectedBuy: false }, // 0.1 probability < 0.3
        { outsCount: OutsCount.LOW, expectedBuy: false }, // 0.1 probability < 0.3
        { outsCount: 5, expectedBuy: false }, // 0.25 probability < 0.3
        { outsCount: OutsCount.MEDIUM, expectedBuy: false }, // 0.25 probability < 0.3
        { outsCount: 10, expectedBuy: true }, // 0.35 probability > 0.3
        { outsCount: OutsCount.HIGH, expectedBuy: true }, // 0.35 probability > 0.3
        { outsCount: 15, expectedBuy: true }, // 0.55 probability > 0.3
    ];

    for (const testCase of testCases) {
        const result = buildBuyInsuranceMsg(testCase.outsCount, 123, 456);
        assert.strictEqual(
            result.is_buy,
            testCase.expectedBuy,
            `For outsCount ${testCase.outsCount}, expected is_buy to be ${testCase.expectedBuy} (higher outs should increase buy probability)`,
        );
    }

    Math.random = originalRandom;
});

test('Message structure validation', async () => {
    const originalRandom = Math.random;

    // Test not buying insurance
    Math.random = () => 1; // Never buy
    let result = buildBuyInsuranceMsg(5, 999, 777);

    assert.strictEqual(result.roomid, 999);
    assert.strictEqual(result.action_seq, 777);
    assert.strictEqual(result.is_buy, false);
    assert.strictEqual(result.amount, undefined);

    // Test buying insurance
    Math.random = () => 0; // Always buy
    result = buildBuyInsuranceMsg(15, 888, 666);

    assert.strictEqual(result.roomid, 888);
    assert.strictEqual(result.action_seq, 666);
    assert.strictEqual(result.is_buy, true);
    assert.ok(typeof result.amount === 'number');
    assert.ok(result.amount > 0);

    Math.random = originalRandom;
});

test('Coverage amount values are correct enum values', async () => {
    const originalRandom = Math.random;
    Math.random = () => 0; // Always buy insurance

    const expectedValues = [
        CoverageAmount.LOW, // 12
        CoverageAmount.MEDIUM_LOW, // 20
        CoverageAmount.MEDIUM_HIGH, // 33
        CoverageAmount.BREAK_EAVEN, // 50
        CoverageAmount.FULL_POT, // 100
    ];

    // Test multiple outsCount values to get different coverage amounts
    const testOutsCounts = [1, 5, 10, 15, 20];

    for (const outsCount of testOutsCounts) {
        for (let i = 0; i < 20; i++) {
            const result = buildBuyInsuranceMsg(outsCount, 123, 456);
            if (result.is_buy && result.amount) {
                assert.ok(
                    expectedValues.includes(result.amount),
                    `Coverage amount ${result.amount} should be one of the valid enum values`,
                );
            }
        }
    }

    Math.random = originalRandom;
});

test('Statistical validation - probability distribution over multiple runs', async () => {
    const originalRandom = Math.random;
    const runs = 1000;

    // Test LOW outsCount - should have ~10% buy rate
    Math.random = originalRandom; // Use real random
    let buyCount = 0;
    for (let i = 0; i < runs; i++) {
        const result = buildBuyInsuranceMsg(OutsCount.LOW, 123, 456);
        if (result.is_buy) buyCount++;
    }
    const buyRate = buyCount / runs;

    // Allow some variance but should be roughly around 0.1 (10%)
    assert.ok(
        buyRate >= 0.05 && buyRate <= 0.15,
        `Buy rate for LOW outsCount should be around 10%, got ${(buyRate * 100).toFixed(1)}%`,
    );

    // Test HIGH outsCount - should have higher buy rate
    buyCount = 0;
    for (let i = 0; i < runs; i++) {
        const result = buildBuyInsuranceMsg(15, 123, 456); // > HIGH threshold
        if (result.is_buy) buyCount++;
    }
    const highBuyRate = buyCount / runs;

    // Should be around 55% for very high outsCount
    assert.ok(
        highBuyRate >= 0.45 && highBuyRate <= 0.65,
        `Buy rate for very HIGH outsCount should be around 55%, got ${(highBuyRate * 100).toFixed(1)}%`,
    );

    // Verify that higher outsCount has higher buy probability
    assert.ok(highBuyRate > buyRate, 'Higher outsCount should result in higher insurance buy probability');
});

test('Comprehensive integration test - realistic poker scenarios', async () => {
    const originalRandom = Math.random;

    // Scenario 1: Opponent has very few outs (strong hand vs weak draw)
    Math.random = () => 0.05; // Below 0.1 threshold
    let result = buildBuyInsuranceMsg(2, 100, 1);
    assert.strictEqual(result.is_buy, true, 'Should buy with low random value');

    Math.random = () => 0.15; // Above 0.1 threshold
    result = buildBuyInsuranceMsg(2, 100, 1);
    assert.strictEqual(result.is_buy, false, 'Should not buy with higher random value');

    // Scenario 2: Opponent has many outs (vulnerable to multiple draws)
    Math.random = () => 0.5; // Below 0.55 threshold for high outs
    result = buildBuyInsuranceMsg(20, 200, 2);
    assert.strictEqual(result.is_buy, true, 'Should buy insurance against many opponent outs');

    Math.random = () => 0.6; // Above 0.55 threshold
    result = buildBuyInsuranceMsg(20, 200, 2);
    assert.strictEqual(result.is_buy, false, 'Should not buy with random above threshold');

    // Scenario 3: Medium risk scenario
    Math.random = () => 0.2; // Below 0.25 threshold for medium outs
    result = buildBuyInsuranceMsg(OutsCount.MEDIUM, 300, 3);
    assert.strictEqual(result.is_buy, true, 'Should buy with medium outs and favorable random');

    Math.random = originalRandom;
});

test('Error handling and robustness', async () => {
    const originalRandom = Math.random;

    // Test with extreme values
    const extremeTests = [
        { outsCount: Number.MAX_SAFE_INTEGER, roomId: 1, action_seq: 1 },
        { outsCount: Number.MIN_SAFE_INTEGER, roomId: 1, action_seq: 1 },
        { outsCount: 0, roomId: Number.MAX_SAFE_INTEGER, action_seq: Number.MAX_SAFE_INTEGER },
        { outsCount: 1, roomId: -1, action_seq: -1 },
    ];

    for (const test of extremeTests) {
        Math.random = () => 0; // Force buying to test coverage selection

        // Should not throw errors
        assert.doesNotThrow(() => {
            const result = buildBuyInsuranceMsg(test.outsCount, test.roomId, test.action_seq);
            assert.ok(typeof result === 'object', 'Should return object for extreme values');
            assert.strictEqual(result.roomid, test.roomId, 'Should preserve roomId');
            assert.strictEqual(result.action_seq, test.action_seq, 'Should preserve action_seq');
        }, `Should handle extreme values: outsCount=${test.outsCount}, roomId=${test.roomId}, action_seq=${test.action_seq}`);
    }

    Math.random = originalRandom;
});

test('selectCoverageAmount - Very HIGH outsCount should prefer higher coverage amounts', async () => {
    const originalRandom = Math.random;
    const results: number[] = [];

    Math.random = () => 0; // Always buy insurance

    for (let i = 0; i < 100; i++) {
        const result = buildBuyInsuranceMsg(15, 123, 456); // outsCount > HIGH
        if (result.is_buy && result.amount) {
            results.push(result.amount);
        }
    }

    const expectedAmounts = [
        CoverageAmount.MEDIUM_HIGH,
        CoverageAmount.BREAK_EAVEN,
        CoverageAmount.FULL_POT
    ];
    for (const amount of results) {
        assert.ok(expectedAmounts.includes(amount),
            `Unexpected coverage amount ${amount} for very HIGH outsCount`);
    }

    Math.random = originalRandom;
});
        const result = buildBuyInsuranceMsg(15, 123, 456); // outsCount > HIGH
        if (result.is_buy && result.amount) {
            results.push(result.amount);
        }
    }

    const expectedAmounts = [CoverageAmount.MEDIUM_HIGH, CoverageAmount.BREAK_EAVEN, CoverageAmount.FULL_POT];
    for (const amount of results) {
        assert.ok(
            expectedAmounts.includes(amount),
            `Unexpected coverage amount ${amount} for very HIGH outsCount`,
        );
    }

    Math.random = originalRandom;
});
