import { logging } from 'shared';
import { OutsCount, CoverageAmount } from './Enum';
import { weightedRandomSelection } from 'shared/src/strategy';

export const calculateInsuranceAmount = (outsCount: number) => {
    const shouldBuy = shouldBuyInsurance(outsCount);

    if (!shouldBuy) {
        return null;
    }

    const coveregeAmountsWaights = getCoverageAmountWeights(outsCount);
    const coverageAmount = weightedRandomSelection(
        coveregeAmountsWaights,
        (entry) => entry.weight,
    ).covarageAmount;

    logging.withTag('INSURANCE_DECISION').info('Insurance decision made', {
        outsCount,
        shouldBuy,
        amount: coverageAmount,
    });

    return coverageAmount;
};

const shouldBuyInsurance = (outsCount: number): boolean => {
    let buyProbability: number;

    if (outsCount <= OutsCount.LOW) {
        buyProbability = 0.1; // Low probability - opponent has few ways to improve
    } else if (outsCount <= OutsCount.MEDIUM) {
        buyProbability = 0.25; // Medium-low probability - moderate opponent threat
    } else if (outsCount <= OutsCount.HIGH) {
        buyProbability = 0.35; // Medium probability - significant opponent threat
    } else {
        buyProbability = 0.55; // High probability - opponent has many ways to improve
    }

    return buyProbability > Math.random();
};

const getCoverageAmountWeights = (outsCount: number) => {
    let coverageWeights: { covarageAmount: CoverageAmount; weight: number }[];

    if (outsCount <= OutsCount.LOW) {
        // Few opponent outs - lower risk, prefer smaller coverage amounts
        return [
            {
                covarageAmount: CoverageAmount.LOW,
                weight: 50,
            },
            {
                covarageAmount: CoverageAmount.MEDIUM_LOW,
                weight: 35,
            },
            {
                covarageAmount: CoverageAmount.MEDIUM_HIGH,
                weight: 15,
            },
        ];
    } else if (outsCount <= OutsCount.MEDIUM) {
        // Medium opponent outs - moderate risk, balanced coverage
        return [
            {
                covarageAmount: CoverageAmount.LOW,
                weight: 30,
            },
            {
                covarageAmount: CoverageAmount.MEDIUM_LOW,
                weight: 40,
            },
            {
                covarageAmount: CoverageAmount.MEDIUM_HIGH,
                weight: 20,
            },
            {
                covarageAmount: CoverageAmount.BREAK_EAVEN,
                weight: 10,
            },
        ];
    } else if (outsCount <= OutsCount.HIGH) {
        // More opponent outs - higher risk, prefer medium to high coverage
        return [
            {
                covarageAmount: CoverageAmount.MEDIUM_LOW,
                weight: 25,
            },
            {
                covarageAmount: CoverageAmount.MEDIUM_HIGH,
                weight: 35,
            },
            {
                covarageAmount: CoverageAmount.BREAK_EAVEN,
                weight: 30,
            },
            {
                covarageAmount: CoverageAmount.FULL_POT,
                weight: 10,
            },
        ];
    } else {
        // Many opponent outs - high risk, prefer higher coverage amounts
        return [
            {
                covarageAmount: CoverageAmount.MEDIUM_HIGH,
                weight: 20,
            },
            {
                covarageAmount: CoverageAmount.BREAK_EAVEN,
                weight: 40,
            },
            {
                covarageAmount: CoverageAmount.FULL_POT,
                weight: 40,
            },
        ];
    }
};
