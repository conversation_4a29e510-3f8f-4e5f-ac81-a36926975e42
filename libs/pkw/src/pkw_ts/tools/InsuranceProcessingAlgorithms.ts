import { logging } from 'shared';
import { OutsCount, CoverageAmount } from './Enum';
import { protocol } from 'proto/gs_protocol';
import IRequestBuyInsurance = protocol.IRequestBuyInsurance;

export const buildBuyInsuranceMsg = (
    outsCount: number,
    roomId: number,
    action_seq: number,
): IRequestBuyInsurance => {
    if (!shouldBuyInsurance(outsCount)) {
        return {
            roomid: roomId,
            action_seq: action_seq,
            is_buy: false,
        };
    }

    const coverageAmount = selectCoverageAmount(outsCount);

    logging.withTag('INSURANCE_DECISION').info('Insurance decision made', {
        outsCount,
        shouldBuy: shouldBuyInsurance,
        amount: coverageAmount,
        roomId,
    });

    return {
        roomid: roomId,
        action_seq: action_seq,
        amount: coverageAmount,
        is_buy: true,
    };
};

const shouldBuyInsurance = (outsCount: number): boolean => {
    let buyProbability: number;

    if (outsCount <= OutsCount.LOW) {
        buyProbability = 0.1; // Low probability - opponent has few ways to improve
    } else if (outsCount <= OutsCount.MEDIUM) {
        buyProbability = 0.25; // Medium-low probability - moderate opponent threat
    } else if (outsCount <= OutsCount.HIGH) {
        buyProbability = 0.35; // Medium probability - significant opponent threat
    } else {
        buyProbability = 0.55; // High probability - opponent has many ways to improve
    }

    return buyProbability > Math.random();
};

const selectCoverageAmount = (outsCount: number): number => {
    let coverageWeights: { [amount: number]: number };

    if (outsCount <= OutsCount.LOW) {
        // Few opponent outs - lower risk, prefer smaller coverage amounts
        coverageWeights = {
            [CoverageAmount.LOW]: 50,
            [CoverageAmount.MEDIUM_LOW]: 35,
            [CoverageAmount.MEDIUM_HIGH]: 15,
        };
    } else if (outsCount <= OutsCount.MEDIUM) {
        // Medium opponent outs - moderate risk, balanced coverage
        coverageWeights = {
            [CoverageAmount.LOW]: 30,
            [CoverageAmount.MEDIUM_LOW]: 40,
            [CoverageAmount.MEDIUM_HIGH]: 20,
            [CoverageAmount.BREAK_EAVEN]: 10,
        };
    } else if (outsCount <= OutsCount.HIGH) {
        // More opponent outs - higher risk, prefer medium to high coverage
        coverageWeights = {
            [CoverageAmount.MEDIUM_LOW]: 25,
            [CoverageAmount.MEDIUM_HIGH]: 35,
            [CoverageAmount.BREAK_EAVEN]: 30,
            [CoverageAmount.FULL_POT]: 10,
        };
    } else {
        // Many opponent outs - high risk, prefer higher coverage amounts
        coverageWeights = {
            [CoverageAmount.MEDIUM_HIGH]: 20,
            [CoverageAmount.BREAK_EAVEN]: 40,
            [CoverageAmount.FULL_POT]: 40,
        };
    }

    return weightedRandomSelection(coverageWeights);
};

/**
 * Performs weighted random selection from a set of coverage amount options
 *
 *  more explanation behind weighted random algoritm could be find here
 *           https://dev.to/jacktt/understanding-the-weighted-random-algorithm-581p
 */
const weightedRandomSelection = (coverageAmountWeights: { [coverageAmount: number]: number }): number => {
    const totalWeightSum = Object.values(coverageAmountWeights).reduce(
        (accumulatedWeight, currentWeight) => accumulatedWeight + currentWeight,
        0,
    );

    const randomThreshold = Math.random() * totalWeightSum;

    let runningWeightSum = 0;

    for (const [coverageAmountStr, weightValue] of Object.entries(coverageAmountWeights)) {
        runningWeightSum += weightValue;

        if (randomThreshold <= runningWeightSum) {
            const selectedCoverageAmount = parseInt(coverageAmountStr, 10);
            return selectedCoverageAmount;
        }
    }

    const fallbackCoverageAmount = parseInt(Object.keys(coverageAmountWeights)[0], 10);
    return fallbackCoverageAmount;
};
